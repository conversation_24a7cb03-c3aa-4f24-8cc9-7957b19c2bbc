import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private readonly zeptoApiUrl = 'https://api.zeptomail.in/v1.1/email';

  constructor(private configService: ConfigService) {}

  async sendOtpEmail(email: string, otp: string, firstName?: string): Promise<boolean> {
    try {
      const payload = {
        bounce_address: this.configService.get('ZEPTO_BOUNCE_ADDRESS'),
        from: {
          address: this.configService.get('ZEPTO_FROM_EMAIL'),
          name: this.configService.get('ZEPTO_FROM_NAME') || 'JymFitnessX'
        },
        to: [
          {
            email_address: {
              address: email,
              name: firstName || 'User'
            }
          }
        ],
        subject: 'Email Verification - JymFitnessX',
        htmlbody: this.getOtpEmailTemplate(otp, firstName),
        textbody: `Your verification code is: ${otp}. This code will expire in 10 minutes.`
      };

      const response = await axios.post(this.zeptoApiUrl, payload, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': `Zoho-enczapikey ${this.configService.get('ZEPTO_API_KEY')}`
        }
      });

      if (response.status === 200) {
        this.logger.log(`OTP email sent successfully to ${email}`);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Failed to send OTP email to ${email}:`, error.response?.data || error.message);
      return false;
    }
  }

  private getOtpEmailTemplate(otp: string, firstName?: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #007bff; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px; background: #f9f9f9; }
          .otp-code { font-size: 32px; font-weight: bold; color: #007bff; text-align: center; 
                     background: white; padding: 20px; margin: 20px 0; border-radius: 8px; 
                     letter-spacing: 5px; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
          .warning { color: #e74c3c; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>JymFitnessX</h1>
            <h2>Email Verification</h2>
          </div>
          <div class="content">
            <h3>Hello ${firstName || 'User'}!</h3>
            <p>Thank you for signing up with JymFitnessX. To complete your registration, please verify your email address using the code below:</p>
            
            <div class="otp-code">${otp}</div>
            
            <p><strong>Important:</strong></p>
            <ul>
              <li>This verification code will expire in <span class="warning">10 minutes</span></li>
              <li>Do not share this code with anyone</li>
              <li>If you didn't request this verification, please ignore this email</li>
            </ul>
            
            <p>If you have any questions, feel free to contact our support team.</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 JymFitnessX. All rights reserved.</p>
            <p>This is an automated email, please do not reply.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}
