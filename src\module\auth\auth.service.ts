import { Injectable, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../shared/module/prisma/prisma.service';
import { EmailService } from '../../shared/services/email.service';
import * as admin from 'firebase-admin';
import { AUTH_TYPE, STATUS } from '@prisma/client';
import {
  VerifyEmailDto,
  SignupDto,
  LoginDto,
  SendVerificationEmailDto,
} from './dto/auth.dto';
import {
  authMessages,
  userMessage,
  userSessionMessage,
} from '../../shared/keys/user.key';
import { generateNumericOtp } from '../../shared/module/bcrypt/bcrypt';

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private emailService: EmailService,
  ) {}

  async signup(
    firebaseUid: string,
    authType: AUTH_TYPE,
    signupData: SignupDto,
  ) {
    try {
      // Get Firebase user data
      const firebaseUser = await admin.auth().getUser(firebaseUid);

      // Check if user already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { firebaseUid, isDeleted: false },
      });

      if (existingUser) {
        throw new BadRequestException(userMessage.USER_ALREADY_EXIST);
      }

      // Check if email already exists with different Firebase UID
      if (firebaseUser.email) {
        const existingEmailUser = await this.prisma.user.findFirst({
          where: {
            email: firebaseUser.email,
            firebaseUid: { not: firebaseUid },
            isDeleted: false,
          },
        });

        if (existingEmailUser) {
          throw new BadRequestException(authMessages.EMAIL_EXIST);
        }
      }

      // Create new user
      const user = await this.prisma.user.create({
        data: {
          firebaseUid,
          email: firebaseUser.email || '',
          emailVerified: firebaseUser.emailVerified || false,
          firstName:
            signupData?.firstName ||
            firebaseUser.displayName?.split(' ')[0] ||
            '',
          lastName:
            signupData?.lastName ||
            firebaseUser.displayName?.split(' ')[1] ||
            '',
          authType,
          profileImage: firebaseUser.photoURL || null,
        },
      });

      // Create user session
      const userSession = await this.prisma.userSession.create({
        data: {
          userId: user.id,
          fcmToken: signupData?.notificationToken || null,
        },
      });

      return {
        status: true,
        message: 'User registered successfully',
        user,
        userSession,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(authMessages.AUTH_ERROR);
    }
  }

  async login(firebaseUid: string, loginData: LoginDto) {
    try {
      // Get Firebase user data
      const firebaseUser = await admin.auth().getUser(firebaseUid);

      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { firebaseUid, isDeleted: false },
      });

      if (!user) {
        throw new BadRequestException(userMessage.USER_NOT_FOUND);
      }

      // Check if user is deleted or disabled
      if (user.isDeleted || user.status === STATUS.DISABLED) {
        throw new BadRequestException(authMessages.ACCOUNT_DEACTIVATED);
      }

      // Create or update user session
      let userSession = await this.prisma.userSession.findFirst({
        where: { userId: user.id, status: STATUS.ENABLED, isDeleted: false },
      });

      if (!userSession) {
        userSession = await this.prisma.userSession.create({
          data: {
            userId: user.id,
            fcmToken: loginData?.notificationToken || null,
          },
        });
      } else if (loginData?.notificationToken) {
        userSession = await this.prisma.userSession.update({
          where: { id: userSession.id },
          data: { fcmToken: loginData.notificationToken },
        });
      }

      return {
        status: true,
        message: 'Login successful',
        user,
        userSession,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(authMessages.AUTH_ERROR);
    }
  }

  async sendVerificationEmail(
    sendVerificationEmailDto: SendVerificationEmailDto,
  ) {
    const { email } = sendVerificationEmailDto;

    // Check if email is already used by a verified user
    const existingUser = await this.prisma.user.findFirst({
      where: { email },
    });

    if (existingUser && existingUser.emailVerified) {
      throw new BadRequestException(authMessages.EMAIL_ALREADY_VERIFIED);
    }

    // Prevent frequent OTP requests
    const recentOtp = await this.prisma.otpVerification.findFirst({
      where: {
        email,
        status: STATUS.ENABLED,
        createdAt: {
          gte: new Date(Date.now() - 2 * 60 * 1000), // last 2 minutes
        },
        isDeleted: false,
      },
    });

    if (recentOtp) {
      throw new BadRequestException(authMessages.OTP_REQUEST_TOO_FREQUENT);
    }

    // Soft-disable existing OTPs
    await this.prisma.otpVerification.updateMany({
      where: {
        email,
        status: STATUS.ENABLED,
        isDeleted: false,
      },
      data: {
        status: STATUS.DISABLED,
        isDeleted: true,
      },
    });

    // Generate new OTP
    const otp = await generateNumericOtp();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes expiry

    // Save OTP
    await this.prisma.otpVerification.create({
      data: {
        email,
        otp,
        expiresAt,
      },
    });

    // Send email
    const emailSent = await this.emailService.sendOtpEmail(
      email,
      otp,
      existingUser?.firstName ?? '', // Optional: user may not exist yet
    );

    if (!emailSent) {
      // Soft-disable OTP if sending fails
      await this.prisma.otpVerification.updateMany({
        where: {
          email,
          otp,
          status: STATUS.ENABLED,
          isDeleted: false,
        },
        data: {
          status: STATUS.DISABLED,
          isDeleted: true,
        },
      });

      throw new BadRequestException(authMessages.EMAIL_SEND_FAILED);
    }

    return {
      status: true,
      message: 'Verification email sent successfully',
    };
  }

  async verifyEmail(firebaseUid: string, verifyEmailDto: VerifyEmailDto) {
    const user = await this.prisma.user.findUnique({
      where: { firebaseUid, isDeleted: false },
    });

    if (!user) {
      throw new BadRequestException(userMessage.USER_NOT_FOUND);
    }

    const otpRecord = await this.prisma.otpVerification.findFirst({
      where: {
        email: user.email,
        otp: verifyEmailDto.otp,
        status: STATUS.ENABLED,
        expiresAt: { gt: new Date() },
        isDeleted: false,
      },
    });

    if (!otpRecord) {
      throw new BadRequestException(authMessages.ENTER_VALID_OTP);
    }

    // Update user email verification status
    await this.prisma.user.update({
      where: { id: user.id },
      data: { emailVerified: true },
    });

    // Disable OTP
    await this.prisma.otpVerification.update({
      where: { id: otpRecord.id },
      data: { status: STATUS.DISABLED, isDeleted: true },
    });

    return {
      status: true,
      message: 'Email verified successfully',
    };
  }

  async logout(firebaseUid: string, sessionId?: string) {
    // Get the user based on firebaseUid
    const user = await this.prisma.user.findUnique({
      where: { firebaseUid, isDeleted: false },
    });

    if (!user) {
      throw new BadRequestException(userMessage.USER_NOT_FOUND);
    }

    if (sessionId) {
      // Verify that the session belongs to the user
      const session = await this.prisma.userSession.findUnique({
        where: { id: sessionId },
      });

      if (!session || session.userId !== user.id || session.isDeleted) {
        throw new BadRequestException(
          userSessionMessage.USER_SESSION_NOT_AUTHORIZED,
        );
      }

      // Mark the session as deleted
      await this.prisma.userSession.update({
        where: { id: sessionId },
        data: { status: STATUS.DISABLED, isDeleted: true },
      });
    } else {
      // Logout all active sessions for the user
      await this.prisma.userSession.updateMany({
        where: { userId: user.id, status: STATUS.ENABLED, isDeleted: false },
        data: { status: STATUS.DISABLED, isDeleted: true },
      });
    }

    return {
      status: true,
      message: authMessages.LOGOUT_SUCCESS,
    };
  }
}
