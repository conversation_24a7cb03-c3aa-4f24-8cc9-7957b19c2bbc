import { Modu<PERSON> } from '@nestjs/common';
import { ThrottlerModule } from '@nestjs/throttler';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from './module/auth/auth.module';
import configuration from 'config/configuration';
import { PrismaModule } from './shared/module/prisma/prisma.module';

@Module({
  imports: [
    ThrottlerModule.forRoot([{ ttl: 60, limit: 10 }]),
    ConfigModule.forRoot({
      envFilePath: '.env',
      load: [configuration],
      cache: true,
      isGlobal: true,
    }),
    ConfigModule.forRoot({ isGlobal: true }),
    PrismaModule,
    AuthModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
