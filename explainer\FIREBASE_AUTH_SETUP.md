# Firebase ID Token Authentication Setup

## Overview

This project is configured to use Firebase ID tokens for authentication instead of traditional JWT tokens. Firebase ID tokens provide secure, stateless authentication with built-in user management and verification.

## Current Implementation

### 1. Firebase Admin SDK Configuration

**File: `src/main.ts`**
```typescript
import * as admin from 'firebase-admin';

// Initialize Firebase Admin SDK
admin.initializeApp({
  credential: admin.credential.cert('./resource/serviceAccountKey.json'),
});
```

### 2. Firebase Auth Guard

**File: `src/auth/guards/firebase-auth.guard.ts`**
- Validates Firebase ID tokens from the `Authorization: Bearer <token>` header
- Uses `admin.auth().verifyIdToken(token)` to verify tokens
- Extracts user information from the decoded token and attaches it to the request

### 3. Authentication Service

**File: `src/auth/auth.service.ts`**
- Uses Firebase UID as the primary user identifier
- Fetches user data from Firebase Admin SDK using `admin.auth().getUser(firebaseUid)`
- Creates or updates local user records based on Firebase user data
- Manages user sessions and FCM tokens

### 4. Authentication Controller

**File: `src/auth/auth.controller.ts`**
- All endpoints are protected with `@UseGuards(FirebaseAuthGuard)`
- Accesses Firebase user data via `req.user.uid` (populated by the guard)
- Supports authentication types: EMAIL, GOOGLE, APPLE

## Key Features

### ✅ Implemented Features

1. **Firebase ID Token Verification**: Validates tokens using Firebase Admin SDK
2. **User Management**: Creates/updates users based on Firebase user data
3. **Session Management**: Tracks user sessions with FCM tokens
4. **Email Verification**: Custom OTP-based email verification system
5. **Multi-Auth Support**: Supports email, Google, and Apple authentication
6. **Logout Functionality**: Manages session termination

### ✅ Security Features

1. **Token Validation**: Firebase handles token expiration and signature verification
2. **User Data Sync**: Automatically syncs user data from Firebase
3. **Session Tracking**: Maintains user sessions in the database
4. **Authorization Headers**: Proper Bearer token validation

## API Endpoints

### Authentication Endpoints

| Method | Endpoint | Description | Headers Required |
|--------|----------|-------------|------------------|
| POST | `/auth` | Authenticate user | `Authorization`, `auth-type`, `notification-token` (optional) |
| POST | `/auth/send-verification-email` | Send OTP for email verification | `Authorization` |
| PATCH | `/auth/verify-email` | Verify email with OTP | `Authorization` |
| PATCH | `/auth/fcm-token` | Update FCM token | `Authorization`, `notification-token` |
| DELETE | `/auth/logout` | Logout user | `Authorization` |

### Request/Response Examples

**Authentication Request:**
```bash
POST /auth
Authorization: Bearer <firebase-id-token>
auth-type: EMAIL
notification-token: <fcm-token>

{
  "firstName": "John",
  "lastName": "Doe"
}
```

**Authentication Response:**
```json
{
  "status": true,
  "message": "Authentication successful",
  "user": {
    "id": "user-id",
    "firebaseUid": "firebase-uid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "emailVerified": true,
    "authType": "EMAIL"
  },
  "userSession": {
    "id": "session-id",
    "userId": "user-id",
    "fcmToken": "fcm-token",
    "status": "ENABLED"
  }
}
```

## Environment Variables

Ensure these environment variables are set:

```env
# Database
DATABASE_URL=mysql://user:password@localhost:3306/database_name

# Email Service (Zepto Mail)
ZEPTO_API_KEY=your-zepto-api-key

# Server
PORT=4000
```

## Firebase Service Account

The Firebase service account key should be placed at:
```
./resource/serviceAccountKey.json
```

This file contains the private key and configuration needed for Firebase Admin SDK authentication.

## Dependencies

### Current Dependencies
- `firebase-admin`: Firebase Admin SDK for server-side operations
- `@nestjs/common`, `@nestjs/core`: NestJS framework
- `@prisma/client`: Database ORM
- `axios`: HTTP client for email service

### Removed Dependencies
- `@nestjs/jwt`: No longer needed (removed)
- `jsonwebtoken`: No longer needed (removed)

## Database Schema

The authentication system uses these Prisma models:

```prisma
model User {
  id            String    @id @default(cuid())
  firebaseUid   String    @unique
  email         String
  emailVerified Boolean   @default(false)
  firstName     String?
  lastName      String?
  authType      AUTH_TYPE
  profileImage  String?
  userSession   UserSession[]
  // ... other fields
}

model UserSession {
  id       String  @id @default(cuid())
  userId   String
  fcmToken String?
  status   STATUS  @default(ENABLED)
  user     User    @relation(fields: [userId], references: [id])
  // ... other fields
}

enum AUTH_TYPE {
  EMAIL
  GOOGLE
  APPLE
}
```

## Testing

To test the authentication:

1. **Get Firebase ID Token**: Use Firebase Auth SDK in your client application
2. **Make API Request**: Include the token in the Authorization header
3. **Verify Response**: Check that user data is returned correctly

## Next Steps

1. **Client Integration**: Ensure your client applications are configured to obtain Firebase ID tokens
2. **Token Refresh**: Implement token refresh logic in client applications
3. **Error Handling**: Add comprehensive error handling for various Firebase Auth scenarios
4. **Monitoring**: Set up logging and monitoring for authentication events

## Troubleshooting

### Common Issues

1. **Invalid Token**: Ensure the Firebase ID token is valid and not expired
2. **Service Account**: Verify the service account key file is correctly placed and has proper permissions
3. **Firebase Project**: Ensure the Firebase project ID matches your configuration
4. **Network Issues**: Check that the server can reach Firebase services

### Debug Tips

1. Check Firebase Admin SDK logs for detailed error messages
2. Verify token format: should be `Bearer <firebase-id-token>`
3. Ensure Firebase project settings match your service account
4. Test with a fresh token from your client application
