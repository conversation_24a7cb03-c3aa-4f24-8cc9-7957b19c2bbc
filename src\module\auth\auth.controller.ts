import {
  Controller,
  Post,
  Patch,
  Delete,
  Body,
  Query,
  UseGuards,
  Req,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { FirebaseAuthGuard } from './guards/firebase-auth.guard';
import {
  VerifyEmailDto,
  SignupDto,
  LoginDto,
  SendVerificationEmailDto,
} from './dto/auth.dto';
import { ApiTags, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('send-verification-email')
  @ApiOperation({ summary: 'Send verification email' })
  async sendVerificationEmail(
    @Body() sendVerificationEmailDto: SendVerificationEmailDto,
  ) {
    return this.authService.sendVerificationEmail(sendVerificationEmailDto);
  }

  @Patch('verify-email')
  @ApiOperation({ summary: 'Verify email with OTP' })
  async verifyEmail(@Req() req: any, @Body() verifyEmailDto: VerifyEmailDto) {
    return this.authService.verifyEmail(req.user.uid, verifyEmailDto);
  }

  @Post('signup')
  @UseGuards(FirebaseAuthGuard)
  @ApiOperation({ summary: 'User Sign-Up' })
  @ApiBearerAuth()
  async signup(@Req() req: any, @Body() signupDto: SignupDto) {
    return this.authService.signup(req.user.uid, signupDto.authType, signupDto);
  }

  @Post('login')
  @UseGuards(FirebaseAuthGuard)
  @ApiOperation({ summary: 'User Login' })
  @ApiBearerAuth()
  async login(@Req() req: any, @Body() loginDto: LoginDto) {
    return this.authService.login(req.user.uid, loginDto);
  }

  @Delete('logout')
  @ApiOperation({ summary: 'Logout user' })
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  async logout(@Req() req: any, @Query('sessionId') sessionId?: string) {
    return this.authService.logout(req.user.uid, sessionId);
  }
}
