import { IsOptional, IsS<PERSON>, <PERSON>E<PERSON>, IsEmail } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { AUTH_TYPE } from '@prisma/client';

export class SignupDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({ enum: AUTH_TYPE })
  @IsEnum(AUTH_TYPE)
  authType: AUTH_TYPE;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  notificationToken?: string;
}

export class LoginDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  notificationToken?: string;
}

export class VerifyEmailDto {
  @ApiProperty()
  @IsString()
  otp: string;

  @ApiProperty()
  @IsEmail()
  @IsString()
  email: string;
}

export class SendVerificationEmailDto {
  @ApiProperty({ description: 'Email address to send verification email to' })
  @IsEmail()
  @IsString()
  email: string;
}

export class UpdateFcmTokenDto {
  @ApiProperty({ description: 'FCM notification token' })
  @IsString()
  notificationToken: string;
}

// Keep the old AuthDto for backward compatibility if needed
export class AuthDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  lastName?: string;
}
